package com.xcwlkj.identityverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.dos.SfhyResultDO;
import com.xcwlkj.identityverify.model.enums.KsrcsjlyEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import com.xcwlkj.identityverify.service.KsKsrcxxService;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw.*;
import com.xcwlkj.identityverify.util.SecretUtils;
import com.xcwlkj.identityverify.util.SfhyUtils;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class MqttSbkkwEventDbHandler {

    @Resource
    private SecretUtils secretUtils;

    @Resource
    private KsKkwMsgService ksKkwMsgService;

    @Resource
    private KsKsrcxxService ksKsrcxxService;

    /**
     * 处理空考位上报
     *
     * @param data      空考位上报请求
     * @param timestamp
     */
    @Transactional(rollbackFor = Exception.class)
    public void sbkkw(JSONObject data, String deviceId, Long timestamp) {
        try {
            log.info("设备序列号[{}] ,上报空考位解密后消息为[{}]", deviceId, data);
            SbkkwReq decryptedData = data.getObject("Data", SbkkwReq.class);
            List<KsKkwMsg> ksKkwMsgs = new ArrayList<>();
            for (SbkkwItemDTO sbkkwData : decryptedData.getDataArray()) {
                String czsj = sbkkwData.getCzsj();
                for (KwlbDTO kwlbData : sbkkwData.getKwlb()) {
                    if (CollectionUtils.isNotEmpty(kwlbData.getKzwhxq())) {
                        boolean flag = true;
                        for (KzwhxqDTO kzwhxqData : kwlbData.getKzwhxq()) {
                            if (StringUtil.isNotBlank(kzwhxqData.getZkzh())) {
                                flag = false;
                                ksKkwMsgs.add(formatKsKkwMsg(timestamp, czsj, sbkkwData, kwlbData, kzwhxqData));
                            }
                        }
                        if (flag) {
                            ksKkwMsgs.add(formatKsKkwMsg(timestamp, czsj, sbkkwData, kwlbData, null)); // Kzwhxq中的所有数据都没有编排考生上报空考位时添加一条无空座位的数据
                        }
                    } else {
                        ksKkwMsgs.add(formatKsKkwMsg(timestamp, czsj, sbkkwData, kwlbData, null)); // Kzwhxq为空时添加一条无空座位的数据
                    }
                }
            }
            log.info("空考位上报数据处理完成");
            ksKkwMsgService.insertListSelective(ksKkwMsgs);
        } catch (Exception e) {
            log.error("处理空考位上报数据异常", e);
            throw e;
        }
    }
    private KsKkwMsg formatKsKkwMsg(Long timestamp, String czsj, SbkkwItemDTO sbkkwData, KwlbDTO kwlbData, KzwhxqDTO kzwhxqData) {
        KsKkwMsg ksKkwMsg = new KsKkwMsg();
        ksKkwMsg.setId(IdGenerateUtil.generateId());
        ksKkwMsg.setKsjhbh(sbkkwData.getExamPlanCode());
        ksKkwMsg.setCcm(sbkkwData.getCcm());
        ksKkwMsg.setBzhkdid(sbkkwData.getOrgCode());
        ksKkwMsg.setBzhkcid(sbkkwData.getBzhkcid());
        ksKkwMsg.setSn(sbkkwData.getSn());
        ksKkwMsg.setSczt(ScztEnum.NOTDEL.getCode());
        ksKkwMsg.setCreateTime(new Date());
        ksKkwMsg.setUpdateTime(new Date());
        ksKkwMsg.setZcqswzm(sbkkwData.getZcqswzm());
        ksKkwMsg.setZwbjfsm(sbkkwData.getZwbjfsm());
        ksKkwMsg.setZwplfsm(sbkkwData.getZwplfsm());
        ksKkwMsg.setLjkch(kwlbData.getLjkch());
        ksKkwMsg.setKch(kwlbData.getKch());
        ksKkwMsg.setDevType(sbkkwData.getDevType());
        if(timestamp!=null){
            ksKkwMsg.setTimestamp(new Date(timestamp));
        }
        ksKkwMsg.setKsKkw(kzwhxqData == null ? "0" : "1");
        if (StringUtils.isNotBlank(czsj)) {
            ksKkwMsg.setCzsj(DateUtil.parseDateTime(czsj));
        }
        if (kzwhxqData != null) {
            ksKkwMsg.setRcbz(kzwhxqData.getRcbz());
            ksKkwMsg.setSjyxj(kzwhxqData.getSjyxj());
            ksKkwMsg.setKsZkzh(kzwhxqData.getZkzh());
            ksKkwMsg.setKsBpzwh(kzwhxqData.getBpzwh());
            ksKkwMsg.setKsSjzwh(kzwhxqData.getSjzwh());
            if (kzwhxqData.getYsbhyxx() != null) {
                SfhyResultDO sfhyResultDO = SfhyUtils.formatHyjg(kzwhxqData.getYsbhyxx().getSzjg(), kzwhxqData.getYsbhyxx().getRlsbjg(), kzwhxqData.getYsbhyxx().getZwrzjg());
                String yzfs = sfhyResultDO.getYzfs(); //验证方式
                String yzjg = sfhyResultDO.getYzjg(); //验证结果
                ksKkwMsg.setYzfs(yzfs);
                ksKkwMsg.setYzjg(yzjg);
                if (StringUtils.equals(kzwhxqData.getYsbhyxx().getHyjg(), SfhySftgEnum.TG.getCode())) {
                    ksKkwMsg.setSfrc(SfhySftgEnum.TG.getCode()); // 核验通过 入场
                    Date yzsjDate = DateUtil.parse(kzwhxqData.getYsbhyxx().getYzsj(), DateUtil.DEFAULT_DATE_TIME);
                    ksKkwMsg.setRcsj(DateUtil.format(yzsjDate, DateUtil.DATETIMEFORMAT2_DEF));
                    ksKkwMsg.setRcsjfz(DateUtil.format(yzsjDate, "yyyyMMddHHmm"));
                } else {
                    // 核验未通过 未入场   浙江模式
                    ksKkwMsg.setSfrc(SfhySftgEnum.WTG.getCode());
                    ksKkwMsg.setRcsj("-");
                    ksKkwMsg.setRcsjfz("-");
                }
            }
            if (kzwhxqData.getYsbrcxx() != null) {
                ksKkwMsg.setRgyzjg(kzwhxqData.getYsbrcxx().getRczt());
            }
        }
        ksKkwMsg.setSjly(KsrcsjlyEnum.PAD.getCode());
        return ksKkwMsg;
    }

}
